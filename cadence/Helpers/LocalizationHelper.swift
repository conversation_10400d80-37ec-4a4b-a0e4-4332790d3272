//
//  LocalizationHelper.swift
//  cadence
//
//  Created by System on 7/24/25.
//

import Foundation

extension String {
    /// 本地化字符串
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
    
    /// 带参数的本地化字符串
    func localized(_ arguments: CVarArg...) -> String {
        return String(format: NSLocalizedString(self, comment: ""), arguments: arguments)
    }
}

/// 本地化助手类
struct LocalizationHelper {
    
    /// 格式化时间
    static func formatTime(_ timeInterval: TimeInterval) -> String {
        let hours = Int(timeInterval) / 3600
        let minutes = Int(timeInterval) % 3600 / 60
        let seconds = Int(timeInterval) % 60
        
        if hours > 0 {
            return "time_format_hms".localized(hours, minutes, seconds)
        } else {
            return "time_format_ms".localized(minutes, seconds)
        }
    }
    
    /// 格式化距离（千米）
    static func formatDistance(_ distance: Double) -> String {
        return "distance_format".localized(distance / 1000)
    }
    
    /// 格式化配速
    static func formatPace(_ pace: Double) -> String {
        return "pace_format".localized(pace)
    }
    
    /// 格式化速度
    static func formatSpeed(_ speed: Double) -> String {
        return "speed_format".localized(speed)
    }
    
    /// 格式化歌曲数量
    static func formatSongsCount(_ count: Int) -> String {
        return "songs_count".localized(count)
    }
    
    /// 格式化剧集数量
    static func formatEpisodesCount(_ count: Int) -> String {
        return "episodes_count".localized(count)
    }
    
    /// 格式化BPM
    static func formatBPM(_ bpm: Int) -> String {
        return "bpm_label".localized(bpm)
    }
}
