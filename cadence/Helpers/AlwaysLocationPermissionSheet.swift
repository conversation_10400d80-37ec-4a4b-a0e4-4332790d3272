//
//  AlwaysLocationPermissionSheet.swift
//  cadence
//
//  Created by System on 7/27/25.
//

import SwiftUI
import CoreLocation

struct AlwaysLocationPermissionSheet: View {
    @Binding var isPresented: Bool
    @Environment(\.openURL) var openURL
    let onRequestPermission: () -> Void
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 标题图标
                VStack(spacing: 16) {
                    Image(systemName: "location.fill.viewfinder")
                        .font(.system(size: 60))
                        .foregroundStyle(.blue.gradient)
                    
                    Text("允许位置访问\n用于后台跟踪")
                        .font(.title2)
                        .fontWeight(.bold)
                        .multilineTextAlignment(.center)
                }
                
                // 说明内容
                VStack(alignment: .leading, spacing: 20) {
                    PermissionReasonRow(
                        icon: "figure.run",
                        title: "持续跟踪",
                        description: "即使应用在后台也能跟踪您的跑步路线"
                    )
                    
                    PermissionReasonRow(
                        icon: "clock.fill",
                        title: "精确计时",
                        description: "记录精确的跑步时长和配速计算"
                    )
                    
                    PermissionReasonRow(
                        icon: "map.fill",
                        title: "完整路线",
                        description: "捕获完整的跑步路径，不错过任何路段"
                    )
                }
                .padding(.horizontal, 20)
                
                Spacer()
                
                // 权限设置步骤
                VStack(alignment: .leading, spacing: 12) {
                    Text("如何启用\"始终\"位置权限：")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        PermissionStepRow(step: "1", text: "点击下方\"允许位置访问\"")
                        PermissionStepRow(step: "2", text: "在弹窗中，选择\"更改为始终允许\"")
                        PermissionStepRow(step: "3", text: "返回应用并开始记录")
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                // 按钮组
                VStack(spacing: 12) {
                    Button {
                        onRequestPermission()
                    } label: {
                        Text("允许位置访问")
                            .fontWeight(.semibold)
                            .foregroundStyle(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 14)
                            .background(.blue.gradient, in: Capsule())
                    }
                    
                    Button {
                        if let appSettingURL = URL(string: UIApplication.openSettingsURLString) {
                            openURL(appSettingURL)
                        }
                    } label: {
                        Text("打开设置")
                            .fontWeight(.medium)
                            .foregroundStyle(.blue)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 14)
                            .background(Color(.systemGray6), in: Capsule())
                    }
                    
                    Button {
                        isPresented = false
                    } label: {
                        Text("稍后")
                            .fontWeight(.medium)
                            .foregroundStyle(.secondary)
                    }
                    .padding(.top, 8)
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            .navigationBarHidden(true)
        }
        .presentationDetents([.height(600)])
        .presentationDragIndicator(.visible)
    }
}

struct PermissionReasonRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(.blue)
                .frame(width: 32)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.body)
                    .foregroundStyle(.secondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
    }
}

struct PermissionStepRow: View {
    let step: String
    let text: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text(step)
                .font(.caption)
                .fontWeight(.bold)
                .foregroundStyle(.white)
                .frame(width: 20, height: 20)
                .background(.blue, in: Circle())
            
            Text(text)
                .font(.body)
                .foregroundStyle(.primary)
                .fixedSize(horizontal: false, vertical: true)
        }
    }
}

#Preview {
    AlwaysLocationPermissionSheet(
        isPresented: .constant(true),
        onRequestPermission: {}
    )
}
