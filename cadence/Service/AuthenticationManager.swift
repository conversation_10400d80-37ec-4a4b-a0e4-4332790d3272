//
//  AuthenticationManager.swift
//  cadence
//
//  Created by Assistant on 7/29/25.
//

import Foundation
import AuthenticationServices
import SwiftUI

class AuthenticationManager: NSObject, ObservableObject {
    @Published var isSignedIn = false
    @Published var userName: String?
    @Published var userEmail: String?
    @Published var userID: String?
    
    override init() {
        super.init()
        checkCurrentAuthenticationState()
    }
    
    func signInWithApple() {
        let request = ASAuthorizationAppleIDProvider().createRequest()
        request.requestedScopes = [.fullName, .email]
        
        let authorizationController = ASAuthorizationController(authorizationRequests: [request])
        authorizationController.delegate = self
        authorizationController.presentationContextProvider = self
        authorizationController.performRequests()
    }
    
    func signOut() {
        // 清除本地存储的用户信息
        UserDefaults.standard.removeObject(forKey: "userID")
        UserDefaults.standard.removeObject(forKey: "userName")
        UserDefaults.standard.removeObject(forKey: "userEmail")
        
        // 更新状态
        DispatchQueue.main.async {
            self.isSignedIn = false
            self.userName = nil
            self.userEmail = nil
            self.userID = nil
        }
    }
    
    private func checkCurrentAuthenticationState() {
        if let userID = UserDefaults.standard.string(forKey: "userID") {
            let appleIDProvider = ASAuthorizationAppleIDProvider()
            appleIDProvider.getCredentialState(forUserID: userID) { [weak self] credentialState, error in
                DispatchQueue.main.async {
                    switch credentialState {
                    case .authorized:
                        // 用户已授权，恢复登录状态
                        self?.isSignedIn = true
                        self?.userID = userID
                        self?.userName = UserDefaults.standard.string(forKey: "userName")
                        self?.userEmail = UserDefaults.standard.string(forKey: "userEmail")
                    case .revoked, .notFound:
                        // 用户已撤销授权或未找到，清除登录状态
                        self?.signOut()
                    default:
                        break
                    }
                }
            }
        }
    }
}

// MARK: - ASAuthorizationControllerDelegate
extension AuthenticationManager: ASAuthorizationControllerDelegate {
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            let userID = appleIDCredential.user
            let fullName = appleIDCredential.fullName
            let email = appleIDCredential.email
            
            // 保存用户信息到本地
            UserDefaults.standard.set(userID, forKey: "userID")
            
            // 处理姓名
            if let fullName = fullName {
                let displayName = [fullName.givenName, fullName.familyName]
                    .compactMap { $0 }
                    .joined(separator: " ")
                
                if !displayName.isEmpty {
                    UserDefaults.standard.set(displayName, forKey: "userName")
                }
            }
            
            // 处理邮箱（只在首次登录时提供）
            if let email = email {
                UserDefaults.standard.set(email, forKey: "userEmail")
            }
            
            // 更新状态
            DispatchQueue.main.async {
                self.isSignedIn = true
                self.userID = userID
                self.userName = UserDefaults.standard.string(forKey: "userName")
                self.userEmail = UserDefaults.standard.string(forKey: "userEmail")
            }
        }
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        print("Apple Sign In Error: \(error.localizedDescription)")
    }
}

// MARK: - ASAuthorizationControllerPresentationContextProviding
extension AuthenticationManager: ASAuthorizationControllerPresentationContextProviding {
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return UIWindow()
        }
        return window
    }
}