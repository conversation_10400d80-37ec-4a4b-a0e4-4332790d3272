//
//  SwiftUIView.swift
//  cadence
//
//  Created by 董诣 on 7/29/25.
//

import SwiftUI

struct RunRecordsListView: View {
    var body: some View {
        VStack {
            Text("run records view")
                .padding()
            Spacer()
            /// list of RunRecord
            List {
                ForEach(0..<10, id: \.self) { index in
                    NavigationLink(destination: RunRecordsView()) {
                        Text("Run Record \(index)")
                            .padding()
                            .background(Color.gray.opacity(0.2))
                            .cornerRadius(8)
                    }
                }
            }
        }
    }
}

struct RunRecordsView: View {
    var body: some View {
        Text("run item")
    }
}


#Preview {
    RunRecordsListView()
}
