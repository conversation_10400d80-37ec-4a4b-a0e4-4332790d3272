//
//  ContentView.swift
//  cadence
//
//  Created by <PERSON> on 12/5/24.
//

import SwiftUI
import AVFoundation
import MediaPlayer
import SwiftData

class MetronomePlayer: ObservableObject {
    
    private var audioPlayer: AVAudioPlayer?
    private var timer: Timer?
    @Published var isPlaying = false
    @Published var hit = false
    private var currentBPM: Double = 180
    
    init() {
        let url = Bundle.main.url(forResource: "metronome", withExtension: "mp3")
        do {
            audioPlayer = try AVAudioPlayer(contentsOf: url!)
            audioPlayer?.numberOfLoops = -1
            audioPlayer?.prepareToPlay()
            audioPlayer?.volume = 1.0  // 设置最大音量
        } catch {
            print(error.localizedDescription)
        }
    }
    
    func startMetronome(bpm: Double) {
        isPlaying = true
        do {
            if #available(iOS 10.0, *) {
                try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [.mixWithOthers])
            } else {
                // Fallback on earlier versions
            }
            try AVAudioSession.sharedInstance().setActive(true)
            
            guard let player = audioPlayer else {
                return
            }
            let interval = 60.0 / bpm
            timer?.invalidate()
            timer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
                self?.hit.toggle()
            }
            // 设置播放速率
            player.enableRate = true
            player.rate = Float(bpm / 180.0)
            player.play()
        } catch {
            print("ops")
        }
        audioPlayer?.play()
    }
    
    func stopMetronome() {
        isPlaying = false
        audioPlayer?.stop()
        timer?.invalidate()
        timer = nil
        hit = false
    }
    
    func updateBPM(_ bpm: Double) {
        currentBPM = bpm
        if isPlaying {
            audioPlayer?.rate = Float(bpm / 180.0)
        }
    }
    
    func setVolume(_ volume: Float) {
        audioPlayer?.volume = volume
    }
}

class MusicPlayer: ObservableObject {
    private var remoteCommandCenter: MPRemoteCommandCenter
    @Published var isPlayingMusic = false
    @Published var currentTrackTitle = "No track playing"
    
    init() {
        remoteCommandCenter = MPRemoteCommandCenter.shared()
        setupRemoteCommands()
    }
    
    private func setupRemoteCommands() {
        // 启用远程控制命令
        remoteCommandCenter.playCommand.addTarget { [weak self] _ in
            self?.playMusic()
            return .success
        }
        
        remoteCommandCenter.pauseCommand.addTarget { [weak self] _ in
            self?.pauseMusic()
            return .success
        }
        
        remoteCommandCenter.nextTrackCommand.addTarget { [weak self] _ in
            self?.nextTrack()
            return .success
        }
        
        remoteCommandCenter.previousTrackCommand.addTarget { [weak self] _ in
            self?.previousTrack()
            return .success
        }
    }
    
    func playMusic() {
        // 检查是否有活动的音乐播放会话
        if !AVAudioSession.sharedInstance().isOtherAudioPlaying {
            // 如果没有其他音频在播放，尝试播放 Apple Music 或播客
            MPMusicPlayerController.systemMusicPlayer.play()
        }
        
        // 更新状态
        isPlayingMusic = true
        updateNowPlayingInfo()
    }
    
    func pauseMusic() {
        // 暂停系统音乐播放器
        MPMusicPlayerController.systemMusicPlayer.pause()
        
        // 更新状态
        isPlayingMusic = false
        updateNowPlayingInfo()
    }
    
    func nextTrack() {
        MPMusicPlayerController.systemMusicPlayer.skipToNextItem()
    }
    
    func previousTrack() {
        MPMusicPlayerController.systemMusicPlayer.skipToPreviousItem()
    }
    
    private func updateNowPlayingInfo() {
        // 更新锁屏和控制中心的播放信息
        var nowPlayingInfo = [String: Any]()
        
        // 获取当前播放的音乐信息
        let musicPlayer = MPMusicPlayerController.systemMusicPlayer
        if let nowPlayingItem = musicPlayer.nowPlayingItem {
            nowPlayingInfo[MPMediaItemPropertyTitle] = nowPlayingItem.title ?? "unknown_title"
            nowPlayingInfo[MPMediaItemPropertyArtist] = nowPlayingItem.artist ?? "unknown_artist"
            nowPlayingInfo[MPMediaItemPropertyAlbumTitle] = nowPlayingItem.albumTitle ?? "unknown_album"
            currentTrackTitle = nowPlayingItem.title ?? "unknown_title"
        } else {
            nowPlayingInfo[MPMediaItemPropertyTitle] = "app_name"
            nowPlayingInfo[MPMediaItemPropertyArtist] = "running_playlist"
            currentTrackTitle = "no_track_playing"
        }
        
        nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackRate] = isPlayingMusic ? 1.0 : 0.0
        nowPlayingInfo[MPNowPlayingInfoPropertyElapsedPlaybackTime] = musicPlayer.currentPlaybackTime
        
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo
    }
}

struct ContentView: View {
    var body: some View {
        MainTabView()
    }
}

#Preview("total") {
    ContentView()
}

#Preview("music player") {
    MediaPlayerView()
}
