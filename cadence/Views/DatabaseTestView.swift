//
//  DatabaseTestView.swift
//  cadence
//
//  Created by System on 7/24/25.
//

import SwiftUI
import SwiftData

struct DatabaseTestView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @Query(sort: \RunRecord.startTime, order: .reverse) private var runRecords: [RunRecord]
    @Query(sort: \GPSLocation.timestamp, order: .reverse) private var gpsLocations: [GPSLocation]
    
    @State private var selectedTab = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部Tab切换
                Picker("数据类型", selection: $selectedTab) {
                    Text("运动记录 (\(runRecords.count))").tag(0)
                    Text("GPS位置 (\(gpsLocations.count))").tag(1)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()
                
                // 内容区域
                TabView(selection: $selectedTab) {
                    // 运动记录列表
                    runRecordsView
                        .tag(0)
                    
                    // GPS位置列表
                    gpsLocationsView
                        .tag(1)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("数据库测试")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("清空数据") {
                        clearAllData()
                    }
                    .foregroundColor(.red)
                }
            }
        }
    }
    
    private var runRecordsView: some View {
        List {
            if runRecords.isEmpty {
                Text("暂无运动记录")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .listRowSeparator(.hidden)
            } else {
                ForEach(runRecords, id: \.id) { record in
                    VStack(alignment: .leading, spacing: 8) {
                        // 第一行：ID和GPS点数
                        HStack {
                            Text("ID: \(record.id.uuidString.prefix(8))...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Text("GPS: \(gpsPointsForRecord(record.id).count)点")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                        
                        // 第二行：时间范围
                        HStack {
                            Text("时间:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(formatTime(record.startTime)) → \(record.endTime != nil ? formatTime(record.endTime!) : "进行中")")
                                .font(.system(.caption, design: .monospaced))
                        }
                        
                        // 第三行：运动数据
                        HStack {
                            VStack(alignment: .leading, spacing: 2) {
                                Text("距离")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                Text("\(String(format: "%.2f", record.distance / 1000))km")
                                    .font(.caption)
                                    .fontWeight(.medium)
                            }
                            
                            Spacer()
                            
                            VStack(alignment: .center, spacing: 2) {
                                Text("时长")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                Text(formatDuration(record.duration))
                                    .font(.caption)
                                    .fontWeight(.medium)
                            }
                            
                            Spacer()
                            
                            VStack(alignment: .trailing, spacing: 2) {
                                Text("配速")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                Text("\(String(format: "%.1f", record.pace))'")
                                    .font(.caption)
                                    .fontWeight(.medium)
                            }
                        }
                    }
                    .padding(.vertical, 4)
                    .listRowSeparator(.visible)
                }
            }
        }
        .listStyle(PlainListStyle())
    }
    
    private var gpsLocationsView: some View {
        List {
            if gpsLocations.isEmpty {
                Text("暂无GPS数据")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .listRowSeparator(.hidden)
            } else {
                ForEach(gpsLocations, id: \.id) { location in
                    VStack(alignment: .leading, spacing: 6) {
                        // 第一行：ID和坐标类型
                        HStack {
                            Text("ID: \(location.id.uuidString.prefix(8))...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Text(location.coordinateType.rawValue)
                                .font(.caption)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.blue.opacity(0.2))
                                .cornerRadius(4)
                        }
                        
                        // 第二行：坐标
                        HStack {
                            Text("坐标:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(String(format: "%.6f", location.latitude)), \(String(format: "%.6f", location.longitude))")
                                .font(.system(.caption, design: .monospaced))
                        }
                        
                        // 第三行：时间和关联记录
                        HStack {
                            VStack(alignment: .leading, spacing: 2) {
                                Text("时间")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                Text(formatTime(location.timestamp))
                                    .font(.system(.caption, design: .monospaced))
                            }
                            
                            Spacer()
                            
                            VStack(alignment: .trailing, spacing: 2) {
                                Text("关联记录")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                Text("\(location.runRecordId.uuidString.prefix(8))...")
                                    .font(.system(.caption, design: .monospaced))
                                    .foregroundColor(.green)
                            }
                        }
                    }
                    .padding(.vertical, 4)
                    .listRowSeparator(.visible)
                }
            }
        }
        .listStyle(PlainListStyle())
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        return formatter.string(from: date)
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    private func gpsPointsForRecord(_ recordId: UUID) -> [GPSLocation] {
        return gpsLocations.filter { $0.runRecordId == recordId }
    }
    
    private func clearAllData() {
        // 删除所有GPS位置点
        for location in gpsLocations {
            modelContext.delete(location)
        }
        
        // 删除所有运动记录
        for record in runRecords {
            modelContext.delete(record)
        }
        
        // 保存更改
        do {
            try modelContext.save()
        } catch {
            print("清空数据失败: \(error)")
        }
    }
}

#Preview {
    DatabaseTestView()
}
