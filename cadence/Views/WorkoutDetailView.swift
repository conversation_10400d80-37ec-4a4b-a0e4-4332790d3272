//
//  WorkoutDetailView.swift
//  cadence
//
//  Created by System on 7/29/25.
//

import SwiftUI
import MapKit
import CoreLocation
import SwiftData

struct WorkoutDetailView: View {
    let runRecord: RunRecord
    
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @State private var gpsLocations: [GPSLocation] = []
    @State private var shareImage: UIImage?
    @State private var showingShareSheet = false
    @State private var isLoading = true
    
    var clLocations: [CLLocation] {
        gpsLocations.map { gpsLocation in
            CLLocation(
                coordinate: CLLocationCoordinate2D(
                    latitude: gpsLocation.latitude,
                    longitude: gpsLocation.longitude
                ),
                altitude: 0,
                horizontalAccuracy: 0,
                verticalAccuracy: 0,
                timestamp: gpsLocation.timestamp
            )
        }
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 运动日期和时间
                VStack(spacing: 8) {
                    Text(runRecord.startTime, style: .date)
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text(runRecord.startTime, style: .time)
                        .font(.title3)
                        .foregroundColor(.secondary)
                    
                    if let endTime = runRecord.endTime {
                        Text("duration_from_to" + " " +
                             DateFormatter.timeFormatter.string(from: runRecord.startTime) + 
                             " - " + 
                             DateFormatter.timeFormatter.string(from: endTime))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.top)
                
                // 地图视图
                if isLoading {
                    VStack {
                        ProgressView("loading_route")
                        Text("loading")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .frame(height: 250)
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemGray6))
                    .cornerRadius(15)
                    .padding(.horizontal)
                } else if clLocations.isEmpty {
                    VStack {
                        Image(systemName: "map")
                            .font(.system(size: 50))
                            .foregroundColor(.gray)
                        Text("no_gps_data")
                            .foregroundColor(.secondary)
                    }
                    .frame(height: 250)
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemGray6))
                    .cornerRadius(15)
                    .padding(.horizontal)
                } else {
                    MapSummaryView(locations: clLocations)
                        .frame(height: 250)
                        .cornerRadius(15)
                        .padding(.horizontal)
                }
                
                // 主要统计数据
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 16) {
                    StatCard(
                        title: "distance",
                        value: String(format: "%.2f", runRecord.distance / 1000),
                        unit: "km",
                        icon: "location",
                        color: .blue
                    )
                    
                    StatCard(
                        title: "time",
                        value: LocalizationHelper.formatTime(runRecord.duration),
                        unit: "",
                        icon: "timer",
                        color: .green
                    )
                    
                    StatCard(
                        title: "pace",
                        value: String(format: "%.1f", runRecord.pace),
                        unit: "min_km",
                        icon: "speedometer",
                        color: .orange
                    )
                    
                    StatCard(
                        title: "average_speed",
                        value: String(format: "%.1f", calculateAverageSpeed()),
                        unit: "km_h",
                        icon: "gauge",
                        color: .purple
                    )
                }
                .padding(.horizontal)
                
                // 详细数据列表
                VStack(spacing: 12) {
                    Text("workout_details")
                        .font(.headline)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    VStack(spacing: 8) {
                        DetailRow(title: "start_time",
                                value: DateFormatter.detailFormatter.string(from: runRecord.startTime))
                        
                        if let endTime = runRecord.endTime {
                            DetailRow(title: "end_time",
                                    value: DateFormatter.detailFormatter.string(from: endTime))
                        }
                        
                        DetailRow(title: "total_distance",
                                value: String(format: "%.3f %@", runRecord.distance / 1000, "km"))
                        
                        DetailRow(title: "total_time",
                                value: LocalizationHelper.formatDetailedTime(runRecord.duration))
                        
                        DetailRow(title: "average_pace",
                                value: String(format: "%.2f %@", runRecord.pace, "min_km"))
                        
                        DetailRow(title: "average_speed",
                                value: String(format: "%.2f %@", calculateAverageSpeed(), "km_h"))
                        
                        if !gpsLocations.isEmpty {
                            DetailRow(title: "gps_points",
                                    value: "\(gpsLocations.count)")
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                }
                .padding(.horizontal)
                
                // 分享按钮
                Button(action: generateAndShareImage) {
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                        Text("share_workout")
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.green)
                    .cornerRadius(15)
                }
                .padding(.horizontal)
                .padding(.bottom)
            }
        }
        .navigationTitle("workout_detail")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            loadGPSLocations()
        }
        .sheet(isPresented: $showingShareSheet) {
            if let image = shareImage {
                ActivityView(activityItems: [image])
            }
        }
    }
    
    private func loadGPSLocations() {
        isLoading = true
        Task {
            let locations = DataManager.shared.fetchGPSLocations(for: runRecord.id, context: modelContext)
            await MainActor.run {
                self.gpsLocations = locations.sorted { $0.timestamp < $1.timestamp }
                self.isLoading = false
            }
        }
    }
    
    private func calculateAverageSpeed() -> Double {
        guard runRecord.duration > 0 else { return 0 }
        return (runRecord.distance / 1000) / (runRecord.duration / 3600)
    }
    
    private func generateAndShareImage() {
        let renderer = ImageRenderer(content: 
            WorkoutShareImageView(
                runRecord: runRecord,
                locations: clLocations
            )
        )
        
        renderer.scale = 3.0
        
        if let image = renderer.uiImage {
            shareImage = image
            showingShareSheet = true
        }
    }
}

struct StatCard: View {
    let title: LocalizedStringKey
    let value: String
    let unit: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title2)
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 2) {
                HStack(alignment: .bottom, spacing: 4) {
                    Text(value)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    if !unit.isEmpty {
                        Text(unit)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct DetailRow: View {
    let title: LocalizedStringKey
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.body)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .font(.body)
                .fontWeight(.medium)
                .foregroundColor(.primary)
        }
    }
}

struct WorkoutShareImageView: View {
    let runRecord: RunRecord
    let locations: [CLLocation]
    
    var body: some View {
        VStack(spacing: 20) {
            // 标题
            VStack(spacing: 8) {
                Text("my_running_record")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text(runRecord.startTime, style: .date)
                    .font(.headline)
                    .foregroundColor(.secondary)
            }
            
            // 地图（如果有GPS数据）
            if !locations.isEmpty {
                MapSummaryView(locations: locations)
                    .frame(height: 200)
                    .cornerRadius(15)
            }
            
            // 统计数据
            HStack(spacing: 20) {
                VStack {
                    Text(String(format: "%.2f", runRecord.distance / 1000))
                        .font(.title)
                        .fontWeight(.bold)
                    Text("km")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                VStack {
                    Text(LocalizationHelper.formatTime(runRecord.duration))
                        .font(.title)
                        .fontWeight(.bold)
                    Text("time")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                VStack {
                    Text(String(format: "%.1f", runRecord.pace))
                        .font(.title)
                        .fontWeight(.bold)
                    Text("min_km")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 应用标识
            Text("generated_by_cadence")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(30)
        .background(Color(.systemBackground))
        .frame(width: 400, height: 600)
    }
}

// MARK: - Extensions
extension DateFormatter {
    static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter
    }()
    
    static let detailFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .medium
        return formatter
    }()
}

extension LocalizationHelper {
    static func formatDetailedTime(_ timeInterval: TimeInterval) -> String {
        let hours = Int(timeInterval) / 3600
        let minutes = Int(timeInterval) % 3600 / 60
        let seconds = Int(timeInterval) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
}

#Preview {
    let runRecord = RunRecord(
        startTime: Date().addingTimeInterval(-3600),
        distance: 5000,
        duration: 1800,
        pace: 6.0
    )
    runRecord.endTime = Date()
    
    return NavigationView {
        WorkoutDetailView(runRecord: runRecord)
    }
    .modelContainer(for: [RunRecord.self, GPSLocation.self], inMemory: true)
}
