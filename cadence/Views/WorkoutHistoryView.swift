//
//  WorkoutHistoryView.swift
//  cadence
//
//  Created by System on 7/29/25.
//

import SwiftUI
import SwiftData

struct WorkoutHistoryView: View {
    @Environment(\.modelContext) private var modelContext
    @State private var runRecords: [RunRecord] = []
    @State private var isLoading = true
    
    var body: some View {
        NavigationView {
            VStack {
                if isLoading {
                    ProgressView("loading")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if runRecords.isEmpty {
                    WorkoutEmptyStateView()
                } else {
                    List(runRecords.sorted(by: { $0.startTime > $1.startTime }), id: \.id) { record in
                        NavigationLink(destination: WorkoutDetailView(runRecord: record)) {
                            WorkoutRowView(runRecord: record)
                        }
                    }
                    .listStyle(PlainListStyle())
                }
            }
            .navigationTitle("workout_history")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                loadRunRecords()
            }
            .refreshable {
                loadRunRecords()
            }
        }
    }
    
    private func loadRunRecords() {
        isLoading = true
        Task {
            let records = DataManager.shared.fetchRunRecords(context: modelContext)
            await MainActor.run {
                self.runRecords = records
                self.isLoading = false
            }
        }
    }
}

struct WorkoutRowView: View {
    let runRecord: RunRecord
    
    var body: some View {
        HStack {
            // 运动类型图标
            VStack {
                Image(systemName: "figure.run")
                    .font(.title2)
                    .foregroundColor(.green)
                    .frame(width: 40, height: 40)
                    .background(Color.green.opacity(0.1))
                    .clipShape(Circle())
            }
            
            VStack(alignment: .leading, spacing: 4) {
                // 日期和时间
                Text(runRecord.startTime, style: .date)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(runRecord.startTime, style: .time)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                // 运动数据摘要
                HStack(spacing: 16) {
                    HStack(spacing: 4) {
                        Image(systemName: "location")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(String(format: "%.2f km", runRecord.distance / 1000))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    HStack(spacing: 4) {
                        Image(systemName: "timer")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(LocalizationHelper.formatTime(runRecord.duration))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    HStack(spacing: 4) {
                        Image(systemName: "speedometer")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(String(format: "%.1f min/km", runRecord.pace))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // 箭头
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.cyan)
        }
        .padding(.vertical, 8)
    }
}

struct WorkoutEmptyStateView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "figure.run")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text("no_workouts_yet")
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text("start_your_first_run")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

#Preview {
    WorkoutHistoryView()
        .modelContainer(for: [RunRecord.self, GPSLocation.self], inMemory: true)
}
