//
//  MenuView.swift
//  cadence
//
//  Created by <PERSON> on 1/23/25.
//
import SwiftUI

struct MenuView: View {
    var body: some View {
        List {
            Section {
                Button(action: sendFeedback) {
                    HStack {
                        Image(systemName: "envelope")
                        Text("send_feedback")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(.gray)
                    }
                }
                
                But<PERSON>(action: shareApp) {
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                        Text("share_with_friends")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(.gray)
                    }
                }
                
                Button(action: rateApp) {
                    HStack {
                        Image(systemName: "star")
                        Text("rate_our_app")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(.gray)
                    }
                }
            }
        }
        .navigationTitle("settings")
    }
    
    private func sendFeedback() {
        if let url = URL(string: "mailto:<EMAIL>") {
            UIApplication.shared.open(url)
        }
    }
    
    private func shareApp() {
        let appURL = URL(string: "https://apps.apple.com/app/your-app-id")!
        let activityVC = UIActivityViewController(
            activityItems: ["Check out this awesome metronome app!", appURL],
            applicationActivities: nil
        )
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first,
           let rootVC = window.rootViewController {
            rootVC.present(activityVC, animated: true)
        }
    }
    
    private func rateApp() {
        if let url = URL(string: "itms-apps://itunes.apple.com/app/your-app-id?action=write-review") {
            UIApplication.shared.open(url)
        }
    }
}

#Preview {
    NavigationView {
        MenuView()
    }
}

