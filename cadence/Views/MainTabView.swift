//
//  MainTabView.swift
//  cadence
//
//  Created by Assistant on 7/26/25.
//

import SwiftUI
import SwiftData
import CoreLocation

struct MainTabView: View {
    @Environment(\.modelContext) private var modelContext
    @State private var selectedTab = 0
    @StateObject private var metronomePlayer = MetronomePlayer()
    @StateObject private var locationManager = LocationManager()
    @State private var sliderValue: Double = 180
    private let feedback = UIImpactFeedbackGenerator(style: .medium)
    
    // 添加运动总结所需的状态变量
    @State private var showRunSummary = false
    @State private var summaryDistance: Double = 0
    @State private var summaryDuration: TimeInterval = 0
    @State private var summaryPace: Double = 0
    @State private var summaryLocations: [CLLocation] = []
    
    var body: some View {
        ZStack {
            // 设置全局背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(.systemBackground),
                    Color(.systemGray6).opacity(0.3)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea(.all)
            
            TabView(selection: $selectedTab) {
                // 第一个 Tab - 节拍器界面
                MetronomeView(player: metronomePlayer, locationManager: locationManager, sliderValue: $sliderValue)
                    .background(Color.clear)
                    .tabItem {
                        VStack {
                            Image(systemName: selectedTab == 0 ? "metronome.fill" : "metronome")
                                .font(.system(size: 20, weight: selectedTab == 0 ? .semibold : .regular))
                            Text("Metronome")
                                .font(.caption)
                        }
                    }
                    .tag(0)
                
                // 第二个 Tab - 空白页面，功能通过中间按钮控制
                EmptyControlView()
                    .background(Color.clear)
                    .tabItem {
                        // 这里放一个透明的占位符，实际功能由悬浮按钮处理
                        VStack {
                            Color.clear
                                .frame(width: 44, height: 44)
                            Text("")
                        }
                    }
                    .tag(1)
                
                // 第三个 Tab - 设置页面
                SettingsView()
                    .background(Color.clear)
                    .tabItem {
                        VStack {
                            Image(systemName: selectedTab == 2 ? "gearshape.fill" : "gearshape")
                                .font(.system(size: 20, weight: selectedTab == 2 ? .semibold : .regular))
                            Text("settings")
                                .font(.caption)
                        }
                    }
                    .tag(2)
            }
            .background(Color.green)
            .onAppear {
                setupTabBarAppearance()
                // 设置LocationManager的ModelContext
                locationManager.setModelContext(modelContext)
            }
            
            // 悬浮的中间控制按钮
            VStack {
                Spacer()
                HStack {
                    Spacer()
                    
                    // 中间的大按钮
                    Button(action: {
                        handleMetronomeToggle()
                    }) {
                        Circle()
                            .fill(metronomePlayer.isPlaying ? Color.green.gradient : Color.cyan.gradient)
                            .frame(width: 70, height: 70)
                            .overlay(
                                Circle()
                                    .stroke(Color.white.gradient, lineWidth: 1)
                            )
                            .overlay(
                                Image(systemName: metronomePlayer.isPlaying ? "figure.run" : "figure.walk")
                                    .symbolEffect(.wiggle, isActive: metronomePlayer.hit)
                                    .font(.system(size: 24, weight: .semibold))
                                    .foregroundColor(.white)
                            )
                            .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
                            .scaleEffect(metronomePlayer.isPlaying ? 1.05 : 1.0)
                            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: metronomePlayer.isPlaying)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    Spacer()
                }
                .padding(.bottom, 0) // 调整到与Tab Bar底部q对齐的位置
            }
        }
        // 添加后台定位权限引导界面
        .sheet(isPresented: $locationManager.showAlwaysPermissionAlert) {
            AlwaysLocationPermissionSheet(
                isPresented: $locationManager.showAlwaysPermissionAlert,
                onRequestPermission: {
                    locationManager.requestAlwaysPermission()
                }
            )
        }
        // 添加运动总结页面
        .sheet(isPresented: $showRunSummary) {
            RunSummaryView(
                distance: summaryDistance,
                duration: summaryDuration,
                pace: summaryPace,
                locations: summaryLocations
            )
        }
    }
    
    private func handleMetronomeToggle() {
        withAnimation {
            feedback.impactOccurred()
            if metronomePlayer.isPlaying {
                // 停止节拍器和GPS追踪
                metronomePlayer.stopMetronome()
                locationManager.stopRecording()
                
                // 显示运动总结
                summaryDistance = locationManager.distance
                summaryDuration = locationManager.duration
                summaryPace = locationManager.pace
                summaryLocations = locationManager.locations
                showRunSummary = true
            } else {
                // 开始节拍器和GPS追踪
                metronomePlayer.startMetronome(bpm: sliderValue)
                locationManager.startRecording()
            }
        }
    }
    
    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        
        // 设置背景
        //appearance.configureWithOpaqueBackground()
        //appearance.backgroundColor = UIColor.systemBackground
        
        // 设置阴影
        appearance.shadowColor = UIColor.black.withAlphaComponent(0.1)
        appearance.shadowImage = UIImage()
        
        // 设置选中状态的颜色
        appearance.stackedLayoutAppearance.selected.iconColor = UIColor.systemGreen
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor.systemGreen,
            .font: UIFont.systemFont(ofSize: 10, weight: .semibold)
        ]
        
        // 设置未选中状态的颜色
        appearance.stackedLayoutAppearance.normal.iconColor = UIColor.systemGray
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.systemGray,
            .font: UIFont.systemFont(ofSize: 10, weight: .regular)
        ]
        
        // 应用外观设置
        UITabBar.appearance().standardAppearance = appearance
        if #available(iOS 15.0, *) {
            UITabBar.appearance().scrollEdgeAppearance = appearance
        }
        
        // 设置 Tab Bar 的整体样式
        UITabBar.appearance().barTintColor = UIColor.systemBackground
        UITabBar.appearance().isTranslucent = false
        UITabBar.appearance().layer.borderWidth = 0.5
        UITabBar.appearance().layer.borderColor = UIColor.systemGray5.cgColor
        UITabBar.appearance().clipsToBounds = true
    }
}

// 空白控制视图，用于中间Tab占位
struct EmptyControlView: View {
    var body: some View {
        VStack {
            Spacer()
            Text("use_floating_button")
                .font(.headline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            Spacer()
        }
        .padding()
    }
}

// 修改后的节拍器视图 - 移除小人按钮
struct MetronomeView: View {
    @Environment(\.modelContext) private var modelContext
    @ObservedObject var player: MetronomePlayer
    @ObservedObject var locationManager: LocationManager
    @Binding var sliderValue: Double
    @StateObject private var musicPlayer = MusicPlayer()
    
    var body: some View {
        ScrollView {
            VStack {
                // 媒体播放器区域 - 顶部
                Spacer()
                MediaPlayerView()
                    .padding(.horizontal)
                    .padding(.top, 10)
                
                Spacer()
                
                // 位置权限提示
                if locationManager.needsLocationPermission {
                    VStack {
                        HStack {
                            Image(systemName: "location.slash")
                                .font(.largeTitle)
                                .foregroundColor(.orange)
                            Text("location_permission_needed")
                                .font(.headline)
                                .multilineTextAlignment(.center)
                        }
                        Text("location_permission_settings")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color(.systemYellow).opacity(0.1))
                    .cornerRadius(10)
                    .padding(.horizontal)
                }
                
                // GPS数据显示区域
                if locationManager.isRecording {
                    VStack(spacing: 10) {
                        HStack {
                            VStack(alignment: .leading) {
                                Text("distance")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Text(String(format: "%.2f %@", locationManager.distance / 1000, "km"))
                                    .font(.title3)
                                    .fontWeight(.bold)
                                    .foregroundColor(.green)
                            }
                            
                            Spacer()
                            
                            VStack {
                                Text("time")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Text(LocalizationHelper.formatTime(locationManager.duration))
                                    .font(.title3)
                                    .fontWeight(.bold)
                                    .foregroundColor(.green)
                            }
                            
                            Spacer()
                            
                            VStack(alignment: .trailing) {
                                Text("pace")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Text(locationManager.pace > 0 ? String(format: "%.1f'", locationManager.pace) : "--")
                                    .font(.title3)
                                    .fontWeight(.bold)
                                    .foregroundColor(.green)
                            }
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(10)
                    }
                    .padding(.horizontal)
                    .transition(.opacity)
                }
                
                Spacer()
                
                // BPM 滑块 - Modern Design
                VStack(spacing: 30) {
                    // Circular BPM Display
                    // CircularBPMView(sliderValue: sliderValue, isRecording: locationManager.isRecording)
                    
                    // Modern Slider
                    ModernSliderView(sliderValue: $sliderValue, isDragging: .constant(false), isRecording: locationManager.isRecording)
                }
                .padding()
            }
            .onAppear {
                locationManager.setModelContext(modelContext)
            }
            .padding()
            .onChange(of: sliderValue) { oldValue, newValue in
                player.updateBPM(newValue)
            }
        }
        .background(Color.clear)
        //.permissionSheet([.location, .music, .photos])
    }
}

// MARK: - Custom Components

// 圆形BPM显示组件
struct CircularBPMView: View {
    let sliderValue: Double
    let isRecording: Bool
    
    var body: some View {
        ZStack {
            // Background Circle
            Circle()
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [Color.gray.opacity(0.2), Color.gray.opacity(0.1)]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 6
                )
                .frame(width: 80, height: 80)
            
            // Progress Circle
            Circle()
                .trim(from: 0, to: CGFloat((sliderValue - 160) / 40))
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: isRecording ?
                                           [Color.green.opacity(0.7), Color.green] :
                                            [Color.blue.opacity(0.7), Color.blue]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    style: StrokeStyle(lineWidth: 6, lineCap: .round)
                )
                .frame(width: 80, height: 80)
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 0.3), value: sliderValue)
            
            // BPM Text
            VStack(spacing: 1) {
                Text("\(Int(sliderValue))")
                    .font(.system(size: 24, weight: .bold, design: .rounded))
                    .foregroundColor(isRecording ? .green : .primary)
                    .animation(.easeInOut(duration: 0.2), value: sliderValue)
                
                Text("BPM")
                    .font(.system(size: 10, weight: .medium, design: .rounded))
                    .foregroundColor(.secondary)
            }
        }
        .scaleEffect(isRecording ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.3), value: isRecording)
    }
}

// 现代滑块组件
struct ModernSliderView: View {
    @Binding var sliderValue: Double
    @Binding var isDragging: Bool
    let isRecording: Bool
    
    var body: some View {
        VStack(spacing: 15) {
            // Custom Slider
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // Background Track
                    RoundedRectangle(cornerRadius: 8)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [Color.gray.opacity(0.2), Color.gray.opacity(0.1)]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(height: 16)
                    
                    // Active Track
                    RoundedRectangle(cornerRadius: 8)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: isRecording ?
                                                   [Color.green.opacity(0.7), Color.green] :
                                                    [Color.blue.opacity(0.7), Color.blue]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: geometry.size.width * CGFloat((sliderValue - 160) / 40), height: 16)
                        .animation(.easeInOut(duration: 0.2), value: sliderValue)
                    
                    // Slider Thumb
                    Circle()
                        .fill(
                            RadialGradient(
                                gradient: Gradient(colors: [Color.white, Color.gray.opacity(0.3)]),
                                center: .center,
                                startRadius: 2,
                                endRadius: 15
                            )
                        )
                        .frame(width: 30, height: 30)
                        .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
                        .scaleEffect(isDragging ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 0.15), value: isDragging)
                        .offset(x: geometry.size.width * CGFloat((sliderValue - 160) / 40) - 15)
                        .gesture(
                            DragGesture()
                                .onChanged { value in
                                    isDragging = true
                                    let newValue = 160 + (value.location.x / geometry.size.width) * 40
                                    sliderValue = max(160, min(200, newValue))
                                }
                                .onEnded { _ in
                                    isDragging = false
                                    // Snap to nearest 5
                                    sliderValue = round(sliderValue / 5) * 5
                                }
                        )
                }
            }
            .frame(height: 30)
            .frame(width: 280)
            
            // Range Labels
            HStack {
                VStack(spacing: 2) {
                    Text("160")
                        .font(.system(size: 12, weight: .medium, design: .rounded))
                        .foregroundColor(.secondary)
                    Text("MIN")
                        .font(.system(size: 10, weight: .medium, design: .rounded))
                        .foregroundColor(.secondary.opacity(0.7))
                }
                Spacer()
                
                Text("BPM: \(Int(sliderValue))")
                    .font(.system(size: 14, weight: .bold, design: .rounded))
                    .foregroundColor(isRecording ? .green : .primary)
                
                Spacer()
                
                VStack(spacing: 2) {
                    Text("200")
                        .font(.system(size: 12, weight: .medium, design: .rounded))
                        .foregroundColor(.secondary)
                    Text("MAX")
                        .font(.system(size: 10, weight: .medium, design: .rounded))
                        .foregroundColor(.secondary.opacity(0.7))
                }
            }
            .frame(width: 280)
        }
    }
}

#Preview {
    MainTabView()
}
