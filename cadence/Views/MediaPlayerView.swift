//
//  MediaPlayerView.swift
//  cadence
//
//  Created by Assistant on 23/7/25.
//

import SwiftUI
import MediaPlayer
import AVFoundation

class MediaPlayerManager: ObservableObject {
    @Published var isPlayingMusic = false
    @Published var currentTrackTitle = "No track playing"
    @Published var currentArtist = "Unknown Artist"
    @Published var currentAlbum = "Unknown Album"
    @Published var selectedTab = 0 // 0 for Apple Music, 1 for Podcasts
    @Published var musicPlaylists: [MPMediaPlaylist] = []
    @Published var podcastShows: [MPMediaItemCollection] = []
    @Published var selectedPlaylist: MPMediaPlaylist?
    @Published var selectedPodcastShow: MPMediaItemCollection?
    @Published var currentArtwork: UIImage? // 添加当前封面图
    
    private var remoteCommandCenter: MPRemoteCommandCenter
    private let musicPlayer = MPMusicPlayerController.systemMusicPlayer
    
    init() {
        remoteCommandCenter = MPRemoteCommandCenter.shared()
        setupRemoteCommands()
        loadMediaLibrary()
    }
    
    private func setupRemoteCommands() {
        remoteCommandCenter.playCommand.addTarget { [weak self] _ in
            self?.playMusic()
            return .success
        }
        
        remoteCommandCenter.pauseCommand.addTarget { [weak self] _ in
            self?.pauseMusic()
            return .success
        }
        
        remoteCommandCenter.nextTrackCommand.addTarget { [weak self] _ in
            self?.nextTrack()
            return .success
        }
        
        remoteCommandCenter.previousTrackCommand.addTarget { [weak self] _ in
            self?.previousTrack()
            return .success
        }
    }
    
    func loadMediaLibrary() {
        // 加载 Apple Music 播放列表
        let playlistsQuery = MPMediaQuery.playlists()
        musicPlaylists = playlistsQuery.collections?.compactMap { $0 as? MPMediaPlaylist } ?? []
        
        // 加载播客节目
        let podcastQuery = MPMediaQuery.podcasts()
        let podcastItems = podcastQuery.items ?? []
        
        // 按节目分组播客
        var showsDict: [String: [MPMediaItem]] = [:]
        for item in podcastItems {
            let showTitle = item.podcastTitle ?? "Unknown Show"
            if showsDict[showTitle] == nil {
                showsDict[showTitle] = []
            }
            showsDict[showTitle]?.append(item)
        }
        
        // 创建播客节目集合
        podcastShows = showsDict.compactMap { (showTitle, items) in
            return MPMediaItemCollection(items: items)
        }
    }
    
    func playMusic() {
        musicPlayer.play()
        isPlayingMusic = true
        updateNowPlayingInfo()
    }
    
    func pauseMusic() {
        musicPlayer.pause()
        isPlayingMusic = false
        updateNowPlayingInfo()
    }
    
    func nextTrack() {
        musicPlayer.skipToNextItem()
        updateNowPlayingInfo()
    }
    
    func previousTrack() {
        musicPlayer.skipToPreviousItem()
        updateNowPlayingInfo()
    }
    
    func playPlaylist(_ playlist: MPMediaPlaylist) {
        selectedPlaylist = playlist
        musicPlayer.setQueue(with: playlist)
        playMusic()
    }
    
    func playPodcastShow(_ show: MPMediaItemCollection) {
        selectedPodcastShow = show
        musicPlayer.setQueue(with: show)
        playMusic()
    }
    
    private func updateNowPlayingInfo() {
        var nowPlayingInfo = [String: Any]()
        
        if let nowPlayingItem = musicPlayer.nowPlayingItem {
            let title = nowPlayingItem.title ?? "unknown_title"
            let artist = nowPlayingItem.artist ?? nowPlayingItem.podcastTitle ?? "unknown_artist"
            let album = nowPlayingItem.albumTitle ?? "unknown_album"
            
            nowPlayingInfo[MPMediaItemPropertyTitle] = title
            nowPlayingInfo[MPMediaItemPropertyArtist] = artist
            nowPlayingInfo[MPMediaItemPropertyAlbumTitle] = album
            
            currentTrackTitle = title
            currentArtist = artist
            currentAlbum = album
            
            // 获取当前播放项的封面图
            if let artwork = nowPlayingItem.artwork {
                currentArtwork = artwork.image(at: CGSize(width: 100, height: 100))
            } else {
                currentArtwork = nil
            }
        } else {
            nowPlayingInfo[MPMediaItemPropertyTitle] = "app_name"
            nowPlayingInfo[MPMediaItemPropertyArtist] = "running_playlist"
            currentTrackTitle = "no_track_playing"
            currentArtist = "unknown_artist"
            currentAlbum = "unknown_album"
        }
        
        nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackRate] = isPlayingMusic ? 1.0 : 0.0
        nowPlayingInfo[MPNowPlayingInfoPropertyElapsedPlaybackTime] = musicPlayer.currentPlaybackTime
        
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo
    }
}

struct MediaPlayerView: View {
    @StateObject private var mediaManager = MediaPlayerManager()
    @State private var isExpanded = false
    @State private var selectedAlbumIndex = 0
    @State private var rotationAngle: Double = 0
    @State private var isRotating = false
    
    var body: some View {
        VStack(spacing: 16) {
            // 主媒体播放区域
            VStack(spacing: 0) {
                
                VStack(spacing: 16) {
                    // Tab 选择器 - 重新设计
                    HStack(spacing: 0) {
                        TabButton(
                            title: "apple_music",
                            icon: "music.note",
                            isSelected: mediaManager.selectedTab == 0,
                            color: .blue
                        ) {
                            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                                mediaManager.selectedTab = 0
                            }
                        }
                        
                        TabButton(
                            title: "podcasts",
                            icon: "mic.fill",
                            isSelected: mediaManager.selectedTab == 1,
                            color: .orange
                        ) {
                            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                                mediaManager.selectedTab = 1
                            }
                        }
                    }
                    .background(Color(.systemGray6))
                    .cornerRadius(25)
                    .padding(.horizontal, 4)

                    // 播放控制区域
                    Spacer(minLength: 40)
                    MediaControlsView(mediaManager: mediaManager)
                    Spacer(minLength: 20)
                    // 展开按钮
                    Button(action: {
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            isExpanded.toggle()
                        }
                    }) {
                        HStack {
                            Text(isExpanded ? "collapse_list" : "expand_list")
                                .font(.subheadline)
                                .fontWeight(.medium)
                            Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                                .font(.system(size: 12, weight: .medium))
                        }
                        .foregroundColor(.secondary)
                        .padding(.vertical, 8)
                        .padding(.horizontal, 16)
                        .background(Color(.systemGray5))
                        .cornerRadius(20)
                    }
                }
                .padding(20)
            }
            .background(
                ZStack {
                    // 背景封面图（如果有的话）
                    if let artwork = mediaManager.currentArtwork {
                        Image(uiImage: artwork)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .blur(radius: 5)
                            .opacity(0.8)
                            .clipped()
                    } else {
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(.systemBackground).opacity(0.8),
                                Color(.systemBackground).opacity(0.95)
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color(.systemBackground).opacity(mediaManager.currentArtwork != nil ? 0.7 : 1.0))
                    }
                }
            )
            .clipShape(RoundedRectangle(cornerRadius: 20))
            .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
            
            // 展开的播放列表区域
            if isExpanded {
                VStack {
                    if mediaManager.selectedTab == 0 {
                        EnhancedAppleMusicView(mediaManager: mediaManager)
                    } else {
                        EnhancedPodcastView(mediaManager: mediaManager)
                    }
                }
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.08), radius: 8, x: 0, y: 4)
                )
                .transition(.asymmetric(
                    insertion: .scale(scale: 0.9).combined(with: .opacity).combined(with: .move(edge: .top)),
                    removal: .scale(scale: 0.9).combined(with: .opacity).combined(with: .move(edge: .top))
                ))
            }
        }
        .onAppear {
            // 请求媒体库访问权限
            MPMediaLibrary.requestAuthorization { status in
                if status == .authorized {
                    DispatchQueue.main.async {
                        mediaManager.loadMediaLibrary()
                    }
                }
            }
            
            // 启动旋转动画
            startRotationAnimation()
        }
        .onChange(of: mediaManager.isPlayingMusic) { _, isPlaying in
            if isPlaying {
                startRotationAnimation()
            } else {
                stopRotationAnimation()
            }
        }
    }
    
    private func startRotationAnimation() {
        guard !isRotating else { return }
        isRotating = true
        
        withAnimation(.linear(duration: 3.0).repeatForever(autoreverses: false)) {
            rotationAngle = 360
        }
    }
    
    private func stopRotationAnimation() {
        isRotating = false
        withAnimation(.easeOut(duration: 1.0)) {
            rotationAngle = 0
        }
    }
}

// MARK: - 新的组件视图

struct TabButton: View {
    let title: LocalizedStringKey
    let icon: String
    let isSelected: Bool
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 14, weight: .medium))
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : .secondary)
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? color.gradient : Color.clear.gradient)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct VinylRecordView: View {
    @ObservedObject var mediaManager: MediaPlayerManager
    @Binding var rotationAngle: Double
    @Binding var isRotating: Bool
    
    var body: some View {
        ZStack {
            // 唱片纹理 - 改为方形纹理
            ForEach(0..<5) { i in
                RoundedRectangle(cornerRadius: CGFloat(6 - i))
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    .frame(width: CGFloat(120 - i * 15), height: CGFloat(120 - i * 15))
            }
            
            // 中心专辑封面 - 改为方形
            ZStack {
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color(.systemBackground))
                    .frame(width: 100, height: 100)
                    .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
                
                // 显示实际的封面图或默认图标
                if let artwork = mediaManager.currentArtwork {
                    Image(uiImage: artwork)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 96, height: 96)
                        .clipShape(RoundedRectangle(cornerRadius: 4))
                } else {
                    // 默认图标
                    if mediaManager.selectedTab == 0 {
                        Image(systemName: "music.note")
                            .font(.system(size: 30, weight: .light))
                            .foregroundColor(.blue)
                    } else {
                        Image(systemName: "mic.fill")
                            .font(.system(size: 30, weight: .light))
                            .foregroundColor(.orange)
                    }
                }
            }
        }
        //.rotationEffect(.degrees(rotationAngle))
        .scaleEffect(mediaManager.isPlayingMusic ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.3), value: mediaManager.isPlayingMusic)
    }
}

struct MediaControlsView: View {
    @ObservedObject var mediaManager: MediaPlayerManager
    
    var body: some View {
        VStack(spacing: 12) {
            // 当前播放信息
            if !mediaManager.currentTrackTitle.isEmpty && mediaManager.currentTrackTitle != "No track playing" {
                VStack(spacing: 4) {
                    Text(mediaManager.currentTrackTitle)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .lineLimit(1)
                        .foregroundColor(.primary)
                    
                    Text(mediaManager.currentArtist)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                .padding(.horizontal)
            }
            
            // 播放控制按钮
            HStack(spacing: 30) {
                ControlButton(
                    icon: "backward.fill",
                    size: 20,
                    color: .secondary
                ) {
                    mediaManager.previousTrack()
                }
                
                ControlButton(
                    icon: mediaManager.isPlayingMusic ? "pause.circle.fill" : "play.circle.fill",
                    size: 50,
                    color: mediaManager.selectedTab == 0 ? .blue : .orange
                ) {
                    if mediaManager.isPlayingMusic {
                        mediaManager.pauseMusic()
                    } else {
                        mediaManager.playMusic()
                    }
                }
                
                ControlButton(
                    icon: "forward.fill",
                    size: 20,
                    color: .secondary
                ) {
                    mediaManager.nextTrack()
                }
            }
        }
    }
}

struct ControlButton: View {
    let icon: String
    let size: CGFloat
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: size, weight: .medium))
                //.foregroundColor(color.gradient)
                .foregroundStyle(color.gradient)
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(1.0)
        .animation(.easeInOut(duration: 0.1), value: color)
    }
}

struct EnhancedAppleMusicView: View {
    @ObservedObject var mediaManager: MediaPlayerManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "music.note")
                    .font(.title2)
                    .foregroundColor(.blue)
                Text("music_library")
                    .font(.title2)
                    .fontWeight(.bold)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            
            if mediaManager.musicPlaylists.isEmpty {
                EmptyStateView(
                    icon: "music.note.list",
                    title: "no_playlists_found",
                    subtitle: "create_playlists_in_music_app",
                    color: .blue
                )
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    LazyHStack(spacing: 16) {
                        ForEach(mediaManager.musicPlaylists.prefix(10), id: \.persistentID) { playlist in
                            PlaylistCard(
                                title: playlist.name ?? "unknown_playlist",
                                subtitle: LocalizationHelper.formatSongsCount(playlist.count),
                                icon: "music.note.list",
                                color: .blue,
                                isSelected: playlist == mediaManager.selectedPlaylist
                            ) {
                                mediaManager.playPlaylist(playlist)
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                }
            }
            
            Spacer(minLength: 20)
        }
        .frame(maxHeight: 250)
    }
}

struct EnhancedPodcastView: View {
    @ObservedObject var mediaManager: MediaPlayerManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "mic.fill")
                    .font(.title2)
                    .foregroundColor(.orange)
                Text("podcast_library")
                    .font(.title2)
                    .fontWeight(.bold)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            
            if mediaManager.podcastShows.isEmpty {
                EmptyStateView(
                    icon: "mic.fill",
                    title: "no_podcasts_found",
                    subtitle: "download_podcasts",
                    color: .orange
                )
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    LazyHStack(spacing: 16) {
                        ForEach(mediaManager.podcastShows.prefix(10), id: \.persistentID) { show in
                            PlaylistCard(
                                title: show.items.first?.podcastTitle ?? "unknown_podcast",
                                subtitle: LocalizationHelper.formatEpisodesCount(show.count),
                                icon: "mic.fill",
                                color: .orange,
                                isSelected: show == mediaManager.selectedPodcastShow
                            ) {
                                mediaManager.playPodcastShow(show)
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                }
            }
            
            Spacer(minLength: 20)
        }
        .frame(maxHeight: 250)
    }
}

struct PlaylistCard: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    color.opacity(0.8),
                                    color.opacity(0.6)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)
                    
                    Image(systemName: icon)
                        .font(.system(size: 30, weight: .medium))
                        .foregroundColor(.white)
                    
                    if isSelected {
                        VStack {
                            Spacer()
                            HStack {
                                Spacer()
                                Image(systemName: "speaker.wave.2.fill")
                                    .font(.system(size: 12))
                                    .foregroundColor(.white)
                                    .padding(4)
                                    .background(Color.black.opacity(0.3))
                                    .clipShape(Circle())
                            }
                        }
                        .frame(width: 80, height: 80)
                    }
                }
                
                VStack(spacing: 2) {
                    Text(title)
                        .font(.caption)
                        .fontWeight(.medium)
                        .lineLimit(2)
                        .multilineTextAlignment(.center)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                .frame(width: 80)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

struct EmptyStateView: View {
    let icon: String
    let title: LocalizedStringKey
    let subtitle: LocalizedStringKey
    let color: Color
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: icon)
                .font(.system(size: 50))
                .foregroundColor(color.opacity(0.6))
            
            VStack(spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: 150)
        .padding()
    }
}

// MARK: - 保留原有的视图以兼容性

struct AppleMusicPlaylistView: View {
    @ObservedObject var mediaManager: MediaPlayerManager
    
    var body: some View {
        EnhancedAppleMusicView(mediaManager: mediaManager)
    }
}

struct PodcastShowListView: View {
    @ObservedObject var mediaManager: MediaPlayerManager
    
    var body: some View {
        EnhancedPodcastView(mediaManager: mediaManager)
    }
}

#Preview {
    MediaPlayerView()
        .padding()
}
