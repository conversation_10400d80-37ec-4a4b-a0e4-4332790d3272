# GPS 后台记录功能测试指南

## 功能概述
已经为 Cadence 应用添加了完整的GPS后台记录功能，包括：
- 后台定位权限管理
- 用户友好的权限引导界面
- 持续的GPS轨迹记录

## 主要改进

### 1. LocationManager 更新
- ✅ 添加了 `allowsBackgroundLocationUpdates` 支持
- ✅ 实现权限升级流程：whenInUse → always
- ✅ 新增状态变量跟踪权限状态
- ✅ 在开始/停止记录时正确管理后台定位

### 2. 权限引导界面
- ✅ 创建了 `AlwaysLocationPermissionSheet.swift`
- ✅ 提供清晰的步骤说明
- ✅ 解释为什么需要后台权限
- ✅ 集成到主界面流程中

### 3. Info.plist 配置
- ✅ 已配置 `UIBackgroundModes` 包含 `location`
- ✅ 包含必要的权限描述字符串

## 测试步骤

### 步骤 1: 初始权限测试
1. 删除应用并重新安装（清除所有权限状态）
2. 启动应用
3. 应该看到标准的权限请求界面
4. 选择"使用App时允许"

### 步骤 2: 后台权限引导测试
1. 点击中央的开始记录按钮
2. 应该弹出后台权限引导界面
3. 界面应显示：
   - 为什么需要后台权限的说明
   - 清晰的设置步骤
   - "允许位置访问"按钮

### 步骤 3: 权限升级测试
1. 在引导界面点击"允许位置访问"
2. 系统应显示权限选择对话框
3. 选择"更改为始终允许"
4. 返回应用，GPS记录应该开始

### 步骤 4: 后台记录测试
1. 开始GPS记录
2. 将应用切换到后台（按Home键或切换到其他应用）
3. 步行或跑步一段距离（至少2-3分钟）
4. 返回应用
5. 检查是否记录了完整的GPS轨迹

### 步骤 5: 数据验证
1. 停止记录
2. 检查记录的数据：
   - 距离计算是否准确
   - 时间记录是否连续
   - GPS点是否完整

## 预期行为

### 正常流程
1. **首次使用**: 请求"使用时"权限
2. **开始记录**: 如果只有"使用时"权限，显示引导界面
3. **权限升级**: 引导用户设置"始终"权限
4. **后台记录**: 即使应用在后台也持续记录GPS

### 权限状态处理
- `notDetermined`: 请求whenInUse权限
- `authorizedWhenInUse`: 显示升级引导界面
- `authorizedAlways`: 正常开始后台记录
- `denied/restricted`: 显示设置引导

## 故障排除

### 如果后台记录不工作：
1. 检查权限是否为"始终允许"
2. 确认Info.plist中的UIBackgroundModes配置
3. 检查设备的"低电量模式"是否开启
4. 验证LocationManager中的allowsBackgroundLocationUpdates设置

### 常见问题：
- **权限对话框不出现**: 检查权限状态，可能需要在设置中手动重置
- **后台记录间断**: 检查系统的后台应用刷新设置
- **GPS精度差**: 确保在空旷环境测试，避免室内或高楼密集区域

## 开发注意事项

### 电池优化
- 后台定位会消耗更多电量
- 已设置合理的distanceFilter (5米)
- 可以考虑添加节能模式选项

### 用户体验
- 权限引导界面提供了清晰的说明
- 支持手动打开设置应用
- 提供"稍后"选项，不强制用户立即设置

### 数据隐私
- 仅在用户主动开始记录时收集位置数据
- GPS数据存储在本地设备上
- 提供了清晰的权限使用说明

## 下一步优化建议

1. **添加电池使用提示**: 告知用户后台GPS会影响电池寿命
2. **智能暂停**: 检测用户停止移动时自动暂停记录
3. **权限状态监控**: 实时监控权限变化并相应调整功能
4. **多语言支持**: 完善本地化字符串（当前使用中文硬编码）

现在你可以构建并测试这个功能了！