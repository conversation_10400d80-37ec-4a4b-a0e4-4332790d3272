# Cadence 项目结构说明

## 项目概述

Cadence 是一个功能丰富的跑步节拍器应用，集成了节拍器、GPS 追踪、音乐播放等多种功能。该应用使用 SwiftUI 和 SwiftData 框架开发，支持 iOS 平台。

## 目录结构

```
cadence/
├── cadence/                    # 主应用模块
│   ├── cadenceApp.swift       # 应用入口文件
│   ├── Info.plist             # 应用配置文件
│   ├── cadence.entitlements   # 应用权限配置
│   ├── InfoPlist.xcstrings    # 应用信息本地化
│   ├── Localizable.xcstrings  # 应用文本本地化
│   ├── metronome.mp3          # 节拍器音效文件
│   ├── Assets.xcassets/       # 应用资源文件（图标、颜色等）
│   ├── Data/                  # 数据模型层
│   ├── Helpers/               # 辅助工具类
│   ├── Service/               # 服务层
│   ├── Views/                 # 视图层
│   └── Preview Content/       # 预览内容资源
├── starter/                    # Widget 扩展模块
├── cadenceTests/              # 单元测试
├── cadenceUITests/            # UI 测试
├── cadence.xcodeproj/         # Xcode 项目文件
└── [配置文件]                  # 项目配置和说明文档
```

## 主要模块详解

### 1. 应用入口 (`cadenceApp.swift`)

**功能：** 应用的主入口文件，负责配置 SwiftData 容器和应用生命周期管理。

**关键组件：**
- `ModelContainer` 配置：管理 `RunRecord` 和 `GPSLocation` 数据模型
- 应用场景配置：设置主窗口和数据容器

### 2. 数据模型层 (`Data/`)

#### `RunRecord.swift`
**功能：** 跑步记录数据模型
- `id`: 唯一标识符
- `startTime`: 开始时间
- `endTime`: 结束时间
- `distance`: 跑步距离（米）
- `duration`: 持续时间（秒）
- `pace`: 配速（分钟/公里）

#### `GPSLocation.swift`
**功能：** GPS 位置数据模型
- `id`: 唯一标识符
- `latitude/longitude`: 经纬度坐标
- `timestamp`: 时间戳
- `coordinateType`: 坐标系类型（WGS84、GCJ02、BD09）
- `runRecordId`: 关联的跑步记录ID

#### `DataManager.swift`
**功能：** 数据管理器，提供数据操作接口
- 获取跑步记录列表
- 获取指定记录的GPS轨迹
- 清空所有数据

### 3. 服务层 (`Service/`)

#### `LocationManager.swift`
**功能：** GPS定位和跑步追踪服务
- 实时GPS定位
- 跑步数据计算（距离、配速、时长）
- 后台定位权限管理
- 轨迹记录和存储

#### `AuthenticationManager.swift`
**功能：** Apple 登录认证服务
- Apple ID 登录/登出
- 用户信息管理
- 认证状态监控

#### `CoordinateTransformer.swift`
**功能：** 坐标系转换服务
- 支持不同坐标系间的转换
- WGS84、GCJ02、BD09 坐标系处理

### 4. 视图层 (`Views/`)

#### 主要视图组件

##### `MainTabView.swift`
**功能：** 主标签页控制器
- 管理应用的主要导航
- 集成节拍器、音乐播放器、设置等功能
- 处理跑步开始/停止逻辑
- 浮动按钮控制

##### `ContentView.swift`
**功能：** 内容视图容器和节拍器功能
- `MetronomePlayer` 类：节拍器音频播放控制
- BPM（节拍）调节（100-260）
- 音频会话管理
- 后台播放支持

##### `MediaPlayerView.swift`
**功能：** 媒体播放器界面
- Apple Music 播放列表集成
- Podcast 播放支持
- 媒体控制（播放/暂停/下一首）
- 唱片视觉效果
- 媒体库访问和播放控制

##### `RunSummaryView.swift`
**功能：** 跑步总结页面
- 跑步数据展示（距离、时间、配速、平均速度）
- 地图路径可视化
- 运动记录分享功能
- 生成分享图片

##### `SettingsView.swift`
**功能：** 设置页面
- Apple ID 登录状态管理
- 运动历史记录查看
- FAQ 帮助文档
- 联系和反馈功能

##### `DatabaseTestView.swift`
**功能：** 数据库测试和管理界面
- 运动记录数据查看
- GPS 轨迹数据查看
- 数据清理功能
- 开发调试工具

##### `MenuView.swift`
**功能：** 菜单界面
- 反馈发送
- 应用分享
- 应用评分
- 外部链接跳转

### 5. 帮助类 (`Helpers/`)

#### `LocalizationHelper.swift`
**功能：** 国际化和本地化支持
- 字符串本地化扩展
- 时间格式化
- 距离格式化
- 配速格式化
- 多语言支持

#### `PermissionSheet.swift`
**功能：** 权限请求界面
- 位置权限申请引导
- 权限状态检查
- 用户友好的权限说明

#### `AlwaysLocationPermissionSheet.swift`
**功能：** 后台定位权限专用界面
- "始终允许"定位权限引导
- 后台追踪功能说明
- 系统设置跳转

## 技术架构特点

### 1. 数据层
- **SwiftData**: 现代化的数据持久化框架
- **Core Location**: GPS 定位和地理计算
- **关系模型**: RunRecord 和 GPSLocation 的一对多关系

### 2. 界面层
- **SwiftUI**: 声明式UI框架
- **MVVM 架构**: 观察者模式和状态管理
- **组件化设计**: 可复用的UI组件

### 3. 服务层
- **单例模式**: LocationManager、AuthenticationManager
- **委托模式**: CLLocationManagerDelegate
- **观察者模式**: ObservableObject 和 @Published

### 4. 功能特性
- **多媒体集成**: AVFoundation 音频播放
- **地图可视化**: MapKit 路径渲染
- **权限管理**: 位置、音乐库访问权限
- **国际化**: 多语言支持
- **分享功能**: 运动数据和图片分享

## 应用流程

### 跑步流程
1. 用户调节 BPM 参数
2. 点击开始按钮
3. 同时启动节拍器和 GPS 追踪
4. 实时显示距离、时间、配速
5. 后台持续记录轨迹数据
6. 停止后显示运动总结
7. 数据保存到 SwiftData

### 数据流向
```
GPS 原始数据 → LocationManager → 计算处理 → SwiftData 存储 → UI 显示
节拍器控制 → MetronomePlayer → AVAudioSession → 音频输出
用户操作 → View → ViewModel/Manager → 业务逻辑 → 数据更新
```

## 开发规范

### 文件命名
- View 文件：`XxxView.swift`
- Manager 文件：`XxxManager.swift`
- Model 文件：直接使用实体名称
- Helper 文件：`XxxHelper.swift`

### 代码组织
- 每个模块职责单一
- 使用扩展分离不同功能
- 遵循 SwiftUI 最佳实践
- 注重代码可读性和维护性

这个架构设计体现了现代iOS开发的最佳实践，具有良好的可扩展性和维护性。
