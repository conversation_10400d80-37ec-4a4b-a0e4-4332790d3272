# Xcode
.DS_Store
*/build/*
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
.idea/
*.hmap
*.xcuserstate
*.xcworkspace
!default.xcworkspace

# CocoaPods
Pods/
Podfile.lock

# Carthage
Carthage/Build

# Swift Package Manager
.build/
.swiftpm/

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output

# Code Injection
injectionforxcode/

# Other
*.moved-aside
*.xcuserstate
*.xcscmblueprint
.env
