#!/bin/bash

# 为Cadence项目添加本地化支持的脚本
# 这个脚本需要在Xcode项目中手动执行以下步骤:

echo "=== Cadence 多语言支持配置指南 ==="
echo ""
echo "请在Xcode中执行以下步骤来完成多语言支持的配置:"
echo ""
echo "1. 打开 cadence.xcodeproj"
echo ""
echo "2. 选择项目根节点 'cadence'"
echo ""
echo "3. 在 'PROJECT' 部分选择 'cadence'"
echo ""
echo "4. 切换到 'Info' 标签页"
echo ""
echo "5. 在 'Localizations' 部分，点击 '+' 按钮添加以下语言:"
echo "   - English (en) - 已存在"
echo "   - Chinese (Simplified) (zh-Hans)"
echo "   - Chinese (Traditional) (zh-Hant)" 
echo "   - Japanese (ja)"
echo ""
echo "6. 对于每个新添加的语言，确保勾选以下文件:"
echo "   - Localizable.strings"
echo "   - InfoPlist.strings"
echo "   - LaunchScreen.storyboard (如果存在)"
echo ""
echo "7. 在项目导航器中，右键点击以下文件夹添加到项目:"
echo "   - cadence/en.lproj/"
echo "   - cadence/zh-Hans.lproj/"
echo "   - cadence/zh-Hant.lproj/"
echo "   - cadence/ja.lproj/"
echo ""
echo "8. 在项目导航器中，将 LocalizationHelper.swift 添加到项目"
echo ""
echo "9. 在 Build Settings 中，确保:"
echo "   - 'Development Language' 设置为 'English'"
echo "   - 'Use Base Internationalization' 已启用"
echo ""
echo "10. 清理并重新构建项目"
echo ""
echo "=== 配置完成后的测试 ==="
echo ""
echo "1. 在iOS模拟器中测试不同语言:"
echo "   Settings -> General -> Language & Region -> iPhone Language"
echo ""
echo "2. 确认应用名称在不同语言下显示正确"
echo ""
echo "3. 确认应用内文本根据系统语言正确显示"
echo ""
echo "=== 已创建的文件 ==="
echo ""
echo "本地化字符串文件:"
echo "✓ cadence/en.lproj/Localizable.strings (英文)"
echo "✓ cadence/zh-Hans.lproj/Localizable.strings (简体中文)"
echo "✓ cadence/zh-Hant.lproj/Localizable.strings (繁体中文)"
echo "✓ cadence/ja.lproj/Localizable.strings (日文)"
echo ""
echo "应用信息本地化文件:"
echo "✓ cadence/en.lproj/InfoPlist.strings (英文)"
echo "✓ cadence/zh-Hans.lproj/InfoPlist.strings (简体中文)"
echo "✓ cadence/zh-Hant.lproj/InfoPlist.strings (繁体中文)"
echo "✓ cadence/ja.lproj/InfoPlist.strings (日文)"
echo ""
echo "辅助文件:"
echo "✓ cadence/Helpers/LocalizationHelper.swift (本地化助手)"
echo "✓ 更新的 cadence/Info.plist (支持多语言配置)"
echo ""
echo "代码修改:"
echo "✓ ContentView.swift - 已更新使用本地化字符串"
echo "✓ MenuView.swift - 已更新使用本地化字符串"
echo "✓ RunSummaryView.swift - 已更新使用本地化字符串"
echo "✓ MediaPlayerView.swift - 已更新使用本地化字符串"
echo "✓ PermissionSheet.swift - 已更新使用本地化字符串"
echo ""
echo "=== 注意事项 ==="
echo ""
echo "1. 默认语言已设置为英文"
echo "2. 所有硬编码的中文字符串已替换为本地化键"
echo "3. 权限描述信息已本地化"
echo "4. 应用名称在不同语言下会显示不同的本地化版本"
echo "5. 如需添加新的文本，请在所有 Localizable.strings 文件中添加对应的键值对"
echo ""
echo "配置完成后，您的应用将支持英文、简体中文、繁体中文和日文四种语言！"
