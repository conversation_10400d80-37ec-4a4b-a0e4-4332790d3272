# Cadence - 跑步节拍器

一个功能丰富的跑步节拍器应用，帮助跑者保持理想的步频。

## 主要功能

### 🎵 智能节拍器功能
- 可调节BPM（100-260）
- 一键开始：同时启动节拍器和GPS追踪
- 实时节拍音效播放
- 步频可视化指示
- 支持后台播放

### 📍 GPS跑步追踪
- **实时GPS定位**：精确记录跑步轨迹
- **路径可视化**：在地图上显示跑步路线
- **实时统计**：距离、时间、配速实时更新
- **智能分析**：自动计算平均速度和配速

### 📊 运动数据统计
- 跑步距离（公里）
- 运动时长（时:分:秒）
- 实时配速（分钟/公里）
- 平均速度（公里/小时）

### 🎨 跑步记录分享
- **一键分享**：生成精美的跑步记录图片
- **包含地图**：跑步路线清晰可见
- **完整数据**：距离、时间、配速等关键信息
- **社交分享**：支持分享到各种社交平台

## 新增功能亮点

1. **一键开始运动**
   - 点击即可同时启动节拍器和GPS追踪
   - 简化的用户界面和操作流程
   - 实时显示运动数据

2. **GPS轨迹记录**
   - 高精度GPS定位
   - 实时轨迹记录和数据计算
   - 智能路径优化

3. **运动数据分析**
   - 多维度数据统计
   - 实时性能监控
   - 完整的运动总结

4. **图片分享功能**
   - 自动生成分享图片
   - 地图轨迹可视化
   - 完整运动数据展示

## 技术特性

- 🍎 **原生iOS开发**：使用SwiftUI和UIKit
- 🗺️ **MapKit集成**：高质量地图和路线显示
- 📱 **CoreLocation**：精确GPS定位服务
- 🎧 **AVFoundation**：高品质音频播放
- 🔄 **后台运行**：支持音频和位置后台更新

## 安装和设置

1. 在Xcode中打开项目
2. 确保添加了以下框架：
   - MapKit.framework
   - CoreLocation.framework
3. 运行项目

## 使用说明

### 一键开始跑步
1. 调节BPM滑块设置步频
2. 点击跑步图标一键开始：
   - 自动播放节拍器音效
   - 同时开启GPS轨迹追踪
   - 实时显示距离、时间、配速
3. 再次点击停止跑步和节拍器
4. 自动显示运动总结（距离>50米时）
5. 一键分享跑步记录

## App Store审核要点

新增的GPS跑步功能显著增强了应用的价值和实用性：

✅ **功能丰富度**：不再是简单的节拍器，而是完整的跑步助手  
✅ **用户价值**：提供GPS轨迹、数据分析、社交分享等实用功能  
✅ **技术复杂性**：集成多个iOS框架，展现技术实力  
✅ **用户体验**：直观的界面设计和流畅的交互体验  
✅ **差异化**：结合节拍器和GPS追踪的独特定位  

## 未来规划

- [ ] HealthKit集成
- [ ] 训练计划制定
- [ ] 社交功能扩展
- [ ] Apple Watch支持
- [ ] 更多音效选择

## 权限说明

- **位置权限**：用于GPS跑步追踪和路线记录
- **音频权限**：用于节拍器音效播放
